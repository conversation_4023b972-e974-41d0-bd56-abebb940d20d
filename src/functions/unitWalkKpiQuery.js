const getUnitWalkKpiMarketQuery = (years, months, whereConditions) => {
  const whereClause = whereConditions.join(" AND ");
  const yearFilter = years && years.length > 0 ? years[0] : "2025";
  const monthFilter = months && months.length > 0 ? months[months.length - 1] : "1";

  const query = `
       with 
dates1 as (Select '${yearFilter}' as Year,last_day('${yearFilter}-${monthFilter.toString().padStart(2, "0")}-01') as monthenddate
,trunc('${yearFilter}-${monthFilter.toString().padStart(2, "0")}-01','MM')first_day_month),
dates as(
    Select Concat((Year-1),'-12-31') as last_year_date,
    trunc(add_months(monthenddate,-1),'Month') beginning_first_date,
    last_day(add_months(monthenddate,-1)) beginning_last_date,
    date_add(cast((select monthenddate from dates1) as date),1) as current_last_date,
    last_day(CAST((select monthenddate from dates1) AS DATE)) AS addition_last_date,
    first_day_month addition_first_date
    from dates1
) ,
cte_property as (select * from (select *,row_number()over(partition by BU order by property_sk desc)rnk
 from gold_dev.edlh.dim_property dp
 left join gold_dev.edlh.vw_income_statement_slicer b ON TRIM(dp.adminbu) = TRIM(b.AdminBU)
 where b.BusinessType='Property Management'and ${whereClause} ) where rnk=1
)
,cte_RegionMarketBeginning As (
    Select  a.regionMarket,sum(b.vena_unit_cnts) beginning
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=property_code
where date between (Select beginning_first_date from dates)
 and (Select beginning_last_date from dates)
 group by all
)
,cte_RegionMarket As (
select distinct regionMarket
from cte_property
),
cte_RegionMarketAdditions as (
Select  a.regionMarket,sum(b.vena_unit_cnts) additions
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=b.property_code
where BeginningOfOperations between (Select addition_first_date from dates) and  (Select addition_last_date from dates)
and date between (Select addition_first_date from dates)  and (Select addition_last_date from dates)
 group by all
) ,
cte_RegionMarketLosses as (
   Select  a.regionMarket,sum(b.vena_unit_cnts) losses
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=b.property_code
where EndOfOperations between (Select addition_first_date from dates) and  (Select addition_last_date from dates)
 and date between (Select addition_first_date from dates)  and (Select addition_last_date from dates)
 group by all
),
cte_RegionMarketCurrent as (
 Select  a.regionMarket,sum(b.vena_unit_cnts) current
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=property_code
where date between (Select (addition_first_date) from dates)  and (Select last_day(addition_last_date)  from dates)
 group by all
)
Select RegionMarket,
ifnull(beginning,0) as beginning,
ifnull(additions,0) as additions,
ifnull(losses,0) as losses,
ifnull(current,0) as current
from cte_regionmarket
left join cte_regionmarketbeginning using(regionMarket)
left join cte_regionmarketadditions using(regionMarket)
left join cte_regionmarketlosses using(regionMarket)
left join cte_regionmarketcurrent using(regionMarket)
where current>0
      `;

  return query;
};

const getUnitWalkKpiRegionQuery = (years, months, whereConditions) => {
  const whereClause = whereConditions.join(" AND ");
  const yearFilter = years && years.length > 0 ? years[0] : "2025";
  const monthFilter = months && months.length > 0 ? months[months.length - 1] : "1";

  const query = `
		with 
dates1 as (Select '${yearFilter}' as Year,last_day('${yearFilter}-${monthFilter.toString().padStart(2, "0")}-01') as monthenddate
,trunc('${yearFilter}-${monthFilter.toString().padStart(2, "0")}-01','MM')first_day_month),
dates as(
    Select Concat((Year-1),'-12-31') as last_year_date,
    trunc(add_months(monthenddate,-1),'Month') beginning_first_date,
    last_day(add_months(monthenddate,-1)) beginning_last_date,
    date_add(cast((select monthenddate from dates1) as date),1) as current_last_date,
    last_day(CAST((select monthenddate from dates1) AS DATE)) AS addition_last_date,
    first_day_month addition_first_date
    from dates1
) ,
cte_property as (select * from (select *,row_number()over(partition by BU order by property_sk desc)rnk
 from gold_dev.edlh.dim_property dp
 left join gold_dev.edlh.vw_income_statement_slicer b ON TRIM(dp.adminbu) = TRIM(b.AdminBU)
 where b.BusinessType='Property Management' and ${whereClause}) where rnk=1
)
,cte_RegionMarketBeginning As (
    Select  a.region,sum(b.vena_unit_cnts) beginning
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=property_code
where date between (Select beginning_first_date from dates)
 and (Select beginning_last_date from dates)
 group by all
)
,cte_RegionMarket As (
select distinct Region
from cte_property
),
cte_RegionMarketAdditions as (
Select  a.region,sum(b.vena_unit_cnts) additions
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=b.property_code
where BeginningOfOperations between (Select addition_first_date from dates) and  (Select addition_last_date from dates)  and
date between (Select addition_first_date from dates)  and (Select addition_last_date from dates)
group by all
) ,
cte_RegionMarketLosses as (
   Select  a.region,sum(b.vena_unit_cnts) losses
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=b.property_code
where EndOfOperations between (Select addition_first_date from dates) and  (Select addition_last_date from dates)
 and date between (Select addition_first_date from dates)  and (Select addition_last_date from dates)
 group by all
),
cte_RegionMarketCurrent as (
 Select  a.region,sum(b.vena_unit_cnts) current
    from cte_property a
  left join (select property_code,date,sum(actuals)vena_unit_cnts from gold_dev.edlh.fact_property_details_report
where typee='Units' group by all)b on a.bu=property_code
where date between (Select (addition_first_date) from dates)  and (Select last_day(addition_last_date)  from dates)
 group by all
)
Select Region,
ifnull(beginning,0) as beginning,
ifnull(additions,0) as additions,
ifnull(losses,0) as losses,
ifnull(current,0) as current
from cte_regionmarket
left join cte_regionmarketbeginning using(Region)
left join cte_regionmarketadditions using(Region)
left join cte_regionmarketlosses using(Region)
left join cte_regionmarketcurrent using(Region)
where current>0
	
	`;

  return query;
};

module.exports = {
  getUnitWalkKpiMarketQuery,
  getUnitWalkKpiRegionQuery,
};
