const { verify } = require("azure-ad-jwt");

const ROLES = {
  ADMIN: "Admin",
  DEVELOPER: "Developer",
  READER: "Reader",
};

const jwtConfig = {
  env: {
    issuer: "https://sts.windows.net/be5cc231-0355-4ece-bc68-9fb5f4c9e31b/",
  },
};

const currentConfig = jwtConfig.env;

const authenticate = async (request, context, allowedRoles = []) => {
  const authorizationHeader = request.headers.get("authorization");
  if (!authorizationHeader) {
    throw new Error("Unauthorized");
  }

  const token = authorizationHeader.split(" ")[1];
  try {
    const tokenResult = await new Promise((resolve, reject) => {
      verify(token, currentConfig.issuer, function (err, result) {
        if (result) {
          resolve(result);
        } else {
          context.log(`Token validation failed: ${err}`);
          reject(err);
        }
      });
    });

    const roles = tokenResult.roles || [];
    if (!roles.some((role) => allowedRoles.includes(role))) {
      throw new Error("Forbidden");
    }

    return tokenResult;
  } catch (error) {
    context.log(`Token validation failed: ${error.message}`);
    throw new Error(`${error.message}`);
  }
};

module.exports = { authenticate, ROLES };
